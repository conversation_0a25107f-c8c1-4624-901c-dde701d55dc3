import { cn } from '@/lib/utils'

interface InlineASCIITextProps {
  text: string
  className?: string
}

export default function InlineASCIIText({ text, className }: InlineASCIITextProps) {
  return (
    <span 
      className={cn(
        "font-mono relative inline-block",
        "bg-gradient-to-r from-orange-500 via-pink-500 to-yellow-500",
        "bg-clip-text text-transparent",
        "before:content-[''] before:absolute before:inset-0",
        "before:bg-gradient-to-r before:from-orange-500/20 before:via-pink-500/20 before:to-yellow-500/20",
        "before:blur-sm before:-z-10",
        className
      )}
    >
      {text}
    </span>
  )
}
