{"name": "nodey", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@react-three/fiber": "^8.15.17", "@reactflow/background": "^11.3.14", "@reactflow/controls": "^11.2.14", "@reactflow/minimap": "^11.7.14", "@types/three": "^0.179.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.344.0", "motion": "^12.23.12", "next": "14.1.0", "ogl": "^1.0.11", "react": "^18", "react-dom": "^18", "reactflow": "^11.10.4", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "three": "^0.167.1", "uuid": "^9.0.1", "zod": "^3.22.4", "zustand": "^4.5.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.8", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}