"use client";

import React, { useRef } from 'react'
import VariableProximity from '@/components/ui/VariableProximity/VariableProximity'

export default function HeroProximityTitle() {
  const containerRef = useRef<HTMLDivElement | null>(null)

  return (
    <div ref={containerRef} style={{ position: 'relative' }} className="select-none">
      <VariableProximity
        label={'Build automations at the speed of thought'}
        className={'variable-proximity-demo [text-shadow:0_2px_20px_rgba(0,0,0,0.25)]'}
        fromFontVariationSettings="'wght' 400, 'opsz' 12, 'wdth' 100"
        toFontVariationSettings="'wght' 1000, 'opsz' 40, 'wdth' 110"
        containerRef={containerRef}
        radius={140}
        falloff="linear"
      />
    </div>
  )
}


