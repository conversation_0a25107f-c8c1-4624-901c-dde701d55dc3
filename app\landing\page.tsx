import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON>, GitBranch, Plug, Z<PERSON>, <PERSON>rk<PERSON> } from 'lucide-react'
import { Suspense, lazy } from 'react'
import Silk from '@/components/ui/Silk/Silk'

// Lazy load heavy components
const LandingFlowPreview = lazy(() => import('@/components/landing/landing-flow-preview'))
const HeroProximityTitle = lazy(() => import('@/components/landing/hero-title'))

export default function LandingPage() {
  return (
    <main className="relative min-h-screen overflow-hidden">
      {/* Simple gradient background instead of heavy Silk component */}
      <div className="pointer-events-none absolute inset-0 z-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900" />
      <div className="relative z-10">
        {/* Hero section */}
        <section className="relative">
          <div className="container mx-auto px-6 py-28">
            <div className="relative mx-auto max-w-4xl text-center z-10">
              <div className="inline-flex items-center gap-2 rounded-full border border-white/20 bg-white/10 px-3 py-1 text-xs text-white/80 backdrop-blur">
                <span>Local‑first workflow automation</span>
              </div>
              <div className="mt-6 text-5xl font-semibold tracking-tight text-white sm:text-6xl font-sans">
                Build automations at the speed of thought
              </div>
              <p className="mx-auto mt-5 max-w-2xl text-base leading-7 text-white/80">
                Nodey is a focused, friction‑free workflow editor. Drag, connect, ship. Your data lives in your browser, your logic is portable, and your ideas go from sketch to ship in minutes.
              </p>
              <div className="mt-8 flex items-center justify-center gap-3">
                <Button asChild>
                  <Link href="/editor">
                    Open the Editor
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button asChild variant="outline">
                  <Link href="/(dashboard)/workflows">View Workflows</Link>
                </Button>
              </div>
            </div>
            {/* Live ReactFlow preview */}
            <div className="mx-auto mt-16 max-w-5xl rounded-xl border border-white/10 bg-white/5 p-3 shadow-[0_10px_40px_rgba(0,0,0,0.2)] backdrop-blur">
              <Suspense fallback={<div className="h-72 bg-white/10 rounded-lg animate-pulse" />}>
                <LandingFlowPreview />
              </Suspense>
            </div>
          </div>
        </section>

        {/* Simplified feature section */}
        <section className="container mx-auto px-6 py-20">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-2xl font-semibold text-white">A tool that stays out of your way</h2>
            <p className="mt-3 text-white/80">Opinionated where it matters, invisible where it doesn't.</p>
          </div>
          <div className="mt-10 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            <div className="group relative overflow-hidden rounded-xl border bg-white p-5 shadow-sm">
              <div className="flex items-center gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-md border bg-white shadow-sm">
                  <GitBranch className="h-5 w-5" />
                </div>
                <h3 className="text-sm font-medium text-gray-900">Sketch with nodes</h3>
              </div>
              <p className="mt-2 text-sm leading-6 text-gray-600">Drop triggers, actions, and logic nodes. Link them like a flowchart.</p>
            </div>
            <div className="group relative overflow-hidden rounded-xl border bg-white p-5 shadow-sm">
              <div className="flex items-center gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-md border bg-white shadow-sm">
                  <Plug className="h-5 w-5" />
                </div>
                <h3 className="text-sm font-medium text-gray-900">Connect anything</h3>
              </div>
              <p className="mt-2 text-sm leading-6 text-gray-600">HTTP endpoints, webhooks, and conditional logic—without glue code.</p>
            </div>
            <div className="group relative overflow-hidden rounded-xl border bg-white p-5 shadow-sm">
              <div className="flex items-center gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-md border bg-white shadow-sm">
                  <Zap className="h-5 w-5" />
                </div>
                <h3 className="text-sm font-medium text-gray-900">Ship instantly</h3>
              </div>
              <p className="mt-2 text-sm leading-6 text-gray-600">Iterate locally. Export or integrate when you're ready—no lock‑in.</p>
            </div>
            <div className="group relative overflow-hidden rounded-xl border bg-white p-5 shadow-sm">
              <div className="flex items-center gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-md border bg-white shadow-sm">
                  <Sparkles className="h-5 w-5" />
                </div>
                <h3 className="text-sm font-medium text-gray-900">Polished by default</h3>
              </div>
              <p className="mt-2 text-sm leading-6 text-gray-600">Thoughtful edges, handles, and states. It feels right out of the box.</p>
            </div>
          </div>
        </section>
      </div>
    </main>
  )
}
