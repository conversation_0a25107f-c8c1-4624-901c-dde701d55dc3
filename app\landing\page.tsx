import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { ArrowRight, GitBranch, Plug, Zap, Network, Clock, Code2, Shield, Server, Package, Sparkles } from 'lucide-react'
import LandingFlowPreview from '@/components/landing/landing-flow-preview'
import Silk from '@/components/ui/Silk/Silk'
import HeroProximityTitle from '@/components/landing/hero-title'
import { Roboto_Flex } from 'next/font/google'

// Register variable axes explicitly
const robotoFlex = Roboto_Flex({ subsets: ['latin'], axes: ['wdth', 'opsz'], weight: 'variable', display: 'swap' })

export default function LandingPage() {
  return (
    <main className="relative min-h-screen overflow-hidden">
      <div className="pointer-events-none absolute inset-0 z-0">
        <Silk speed={5} scale={1} color="#7B7481" noiseIntensity={1.5} rotation={0} />
      </div>
      <div className="relative z-10">
      {/* Hero with subtle effects */}
      <section className="relative">
        <div className="container mx-auto px-6 py-28">
            <div className="relative mx-auto max-w-4xl text-center z-10">
            <div className="inline-flex items-center gap-2 rounded-full border border-white/20 bg-white/10 px-3 py-1 text-xs text-white/80 backdrop-blur">
              <span>Local‑first workflow automation</span>
            </div>
            <div className={`mt-6 text-5xl font-semibold tracking-tight text-white sm:text-6xl ${robotoFlex.className}`}>
              <HeroProximityTitle />
            </div>
            <p className="mx-auto mt-5 max-w-2xl text-base leading-7 text-white/80">
              Nodey is a focused, friction‑free workflow editor. Drag, connect, ship. Your data lives in your browser, your logic is portable, and your ideas go from sketch to ship in minutes.
            </p>
            <div className="mt-8 flex items-center justify-center gap-3">
              <Button asChild>
                <Link href="/editor">
                  Open the Editor
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/(dashboard)/workflows">View Workflows</Link>
              </Button>
            </div>
          </div>
          {/* Live ReactFlow preview */}
          <div className="mx-auto mt-16 max-w-5xl rounded-xl border border-white/10 bg-white/5 p-3 shadow-[0_10px_40px_rgba(0,0,0,0.2)] backdrop-blur">
            <LandingFlowPreview />
          </div>
        </div>
      </section>

      {/* Bento feature grid */}
      <section className="container mx-auto px-6 py-20">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-2xl font-semibold text-white">A tool that stays out of your way</h2>
          <p className="mt-3 text-white/80">Opinionated where it matters, invisible where it doesn’t.</p>
        </div>
        <div className="mt-10 grid grid-cols-1 gap-6 md:grid-cols-6">
          <BentoCard className="md:col-span-4" title="Sketch with nodes" icon={<GitBranch className="h-5 w-5" />}>
            <p>Drop triggers, actions, and logic nodes. Link them like a flowchart; Nodey turns it into runnable workflows.</p>
            <div className="mt-4 h-20 rounded-md bg-[radial-gradient(circle_at_1px_1px,_#e5e7eb_1px,_transparent_0)] [background-size:16px_16px]" />
          </BentoCard>
          <BentoCard className="md:col-span-2" title="Connect anything" icon={<Plug className="h-5 w-5" />}>
            <p>HTTP endpoints, webhooks, and conditional logic—without glue code.</p>
            <div className="mt-4 flex items-center gap-2 text-[11px] text-gray-500">
              <span className="inline-flex h-6 items-center rounded-md border px-2">HTTP</span>
              <span className="inline-flex h-6 items-center rounded-md border px-2">Webhook</span>
              <span className="inline-flex h-6 items-center rounded-md border px-2">Transform</span>
            </div>
          </BentoCard>
          <BentoCard className="md:col-span-2" title="Ship instantly" icon={<Zap className="h-5 w-5" />}>
            <p>Iterate locally. Export or integrate when you’re ready—no lock‑in.</p>
            <div className="mt-4 h-2 w-32 animate-pulse rounded-full bg-gradient-to-r from-primary/20 via-primary/50 to-primary/20" />
          </BentoCard>
          <BentoCard className="md:col-span-4" title="Polished by default" icon={<Sparkles className="h-5 w-5" />}>
            <p>Thoughtful edges, handles, and states. It feels right out of the box.</p>
            <div className="mt-4 grid grid-cols-3 gap-2 text-[11px] text-gray-500">
              <span className="rounded-md border px-2 py-1 text-center">Minimap</span>
              <span className="rounded-md border px-2 py-1 text-center">Controls</span>
              <span className="rounded-md border px-2 py-1 text-center">Shortcuts</span>
            </div>
          </BentoCard>
        </div>
      </section>

      {/* Use cases */}
      <section className="border-y bg-white/70 py-16">
        <div className="container mx-auto px-6">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-2xl font-semibold text-gray-900">
              Where <span className="text-primary font-bold">Nodey</span> shines
            </h2>
          </div>
          <div className="mt-10 grid gap-6 md:grid-cols-3">
            <Card
              icon={<Network className="h-5 w-5 text-primary" />}
              title="Ops glue"
              desc="Wire SaaS APIs, internal tools, and webhooks into reliable runbooks without writing yet another microservice."
            />
            <Card
              icon={<Clock className="h-5 w-5 text-primary" />}
              title="Human‑in‑the‑loop"
              desc="Branch approvals, retries, and conditional paths that reflect the messy real world."
            />
            <Card
              icon={<Code2 className="h-5 w-5 text-primary" />}
              title="Prototyping"
              desc="Prove the flow first, then harden. Export and migrate pieces to code when you’re ready."
            />
          </div>
        </div>
      </section>

      {/* Under the hood */}
      <section className="container mx-auto px-6 py-16">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-2xl font-semibold text-gray-900">Built for builders</h2>
          <p className="mt-3 text-gray-600">Simple surface, honest internals.</p>
        </div>
        <div className="mt-10 grid gap-6 md:grid-cols-3">
          <Card
            icon={<Shield className="h-5 w-5 text-primary" />}
            title="Local‑first by default"
            desc="Workflows are stored in your browser while you iterate. Your ideas stay yours."
          />
          <Card
            icon={<Server className="h-5 w-5 text-primary" />}
            title="Straightforward runtime"
            desc="HTTP in, HTTP out. A transparent executor with logs—not a magic black box."
          />
          <Card
            icon={<Package className="h-5 w-5 text-primary" />}
            title="Modern stack"
            desc="Next.js, React Flow, Tailwind. Fast enough to feel instant; familiar enough to extend."
          />
        </div>
      </section>

      {/* CTA */}
      <section className="border-t bg-white/80">
        <div className="container mx-auto px-6 py-12">
          <div className="flex flex-col items-center justify-between gap-4 rounded-lg border bg-white px-6 py-8 shadow-sm md:flex-row">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Open the editor and try a flow</h3>
              <p className="text-sm text-gray-600">No signup. Your first workflow is seconds away.</p>
            </div>
            <div className="flex gap-3">
              <Button asChild>
                <Link href="/editor">
                  Start building
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/(dashboard)/workflows">Browse workflows</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
      </div>
    </main>
  )
}

function FeatureCard({ icon, title, desc }: { icon: React.ReactNode; title: string; desc: string }) {
  return (
    <div className="rounded-lg border bg-white p-5 shadow-sm">
      <div className="flex items-center gap-2">
        <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary/10">
          {icon}
        </div>
        <h3 className="text-sm font-medium text-gray-900">{title}</h3>
      </div>
      <p className="mt-2 text-sm leading-6 text-gray-600">{desc}</p>
    </div>
  )
}

function Card({ icon, title, desc }: { icon: React.ReactNode; title: string; desc: string }) {
  return (
    <div className="rounded-lg border bg-white p-5 shadow-sm">
      <div className="flex items-center gap-2">
        <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary/10">
          {icon}
        </div>
        <h3 className="text-sm font-medium text-gray-900">{title}</h3>
      </div>
      <p className="mt-2 text-sm leading-6 text-gray-600">{desc}</p>
    </div>
  )
}

function BentoCard({
  title,
  icon,
  className,
  children,
}: {
  title: string
  icon: React.ReactNode
  className?: string
  children: React.ReactNode
}) {
  return (
    <div
      className={`group relative overflow-hidden rounded-xl border bg-white p-5 shadow-sm ${className || ''}`}
      style={{
        backgroundImage:
          'linear-gradient(110deg, rgba(59,130,246,0.06), rgba(59,130,246,0) 40%), linear-gradient(0deg, rgba(255,255,255,1), rgba(255,255,255,0.85))',
      }}
    >
      <div className="absolute -right-12 -top-12 h-24 w-24 rounded-full bg-primary/10 blur-2xl transition-all duration-500 group-hover:scale-150" />
      <div className="flex items-center gap-2">
        <div className="flex h-8 w-8 items-center justify-center rounded-md border bg-white shadow-sm">
          {icon}
        </div>
        <h3 className="text-sm font-medium text-gray-900">{title}</h3>
      </div>
      <div className="mt-2 text-sm leading-6 text-gray-600">
        {children}
      </div>
    </div>
  )
}


