import type { <PERSON>ada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import '@/styles/globals.css'
import { ToasterProvider } from '@/components/ui/toaster'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Nodey - Workflow Automation Builder',
  description: 'A simple low-code node builder for workflow automation',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ToasterProvider>
          {children}
        </ToasterProvider>
      </body>
    </html>
  )
}
